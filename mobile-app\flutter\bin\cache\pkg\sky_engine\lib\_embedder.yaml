# This file is generated by //flutter/sky/packages/sky_engine:_embedder_yaml
# Do not modify this file directly. Instead, update the build file.

embedded_libs:
  "dart:async": "async/async.dart"
  "dart:collection": "collection/collection.dart"
  "dart:concurrent": "concurrent/concurrent.dart"
  "dart:convert": "convert/convert.dart"
  "dart:core": "core/core.dart"
  "dart:developer": "developer/developer.dart"
  "dart:ffi": "ffi/ffi.dart"
  "dart:html": "html/html_dart2js.dart"
  "dart:io": "io/io.dart"
  "dart:isolate": "isolate/isolate.dart"
  "dart:js": "js/js.dart"
  "dart:js_interop": "js_interop/js_interop.dart"
  "dart:js_interop_unsafe": "js_interop_unsafe/js_interop_unsafe.dart"
  "dart:js_util": "js_util/js_util.dart"
  "dart:math": "math/math.dart"
  "dart:typed_data": "typed_data/typed_data.dart"
  "dart:ui": "ui/ui.dart"
  "dart:ui_web": "ui_web/ui_web.dart"

  "dart:_http": "_http/http.dart"
  "dart:_interceptors": "_interceptors/interceptors.dart"
  # The _internal library is needed as some implementations bleed into the
  # public API, e.g. List being Iterable by virtue of implementing
  # EfficientLengthIterable. Not including this library yields analysis errors.
  "dart:_internal": "internal/internal.dart"
  # The _js_annotations library is also needed for the same reasons as _internal.
  "dart:_js_annotations": "_js_annotations/_js_annotations.dart"
  # The _js_types library is also needed for the same reasons as _internal.
  "dart:_js_types": "_js_types/js_types.dart"
  "dart:nativewrappers": "_empty.dart"
