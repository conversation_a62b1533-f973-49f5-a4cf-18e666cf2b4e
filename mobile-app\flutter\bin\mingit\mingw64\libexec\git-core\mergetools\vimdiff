diff_cmd () {
	"$merge_tool_path" -R -f -d \
		-c 'wincmd l' -c 'cd $GIT_PREFIX' "$LOCAL" "$REMOTE"
}

merge_cmd () {
	case "$1" in
	gvimdiff|vimdiff)
		if $base_present
		then
			"$merge_tool_path" -f -d -c '4wincmd w | wincmd J' \
				"$LOCAL" "$BASE" "$REMOTE" "$MERGED"
		else
			"$merge_tool_path" -f -d -c 'wincmd l' \
				"$LOCAL" "$MERGED" "$REMOTE"
		fi
		;;
	gvimdiff2|vimdiff2)
		"$merge_tool_path" -f -d -c 'wincmd l' \
			"$LOCAL" "$MERGED" "$REMOTE"
		;;
	gvimdiff3|vimdiff3)
		if $base_present
		then
			"$merge_tool_path" -f -d -c 'hid | hid | hid' \
				"$LOCAL" "$REMOTE" "$BASE" "$MERGED"
		else
			"$merge_tool_path" -f -d -c 'hid | hid' \
				"$LOCAL" "$REMOTE" "$MERGED"
		fi
		;;
	esac
}

translate_merge_tool_path() {
	case "$1" in
	gvimdiff|gvimdiff2|gvimdiff3)
		echo gvim
		;;
	vimdiff|vimdiff2|vimdiff3)
		echo vim
		;;
	esac
}

exit_code_trustable () {
	true
}
