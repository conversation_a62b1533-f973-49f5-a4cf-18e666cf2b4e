diff_cmd () {
	"$merge_tool_path" \
		-R 'Accel.Search: "Ctrl+F"' \
		-R 'Accel.SearchForward: "Ctrl+G"' \
		"$LOCAL" "$REMOTE"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" -X --show-merged-pane \
			-R 'Accel.SaveAsMerged: "Ctrl+S"' \
			-R 'Accel.Search: "Ctrl+F"' \
			-R 'Accel.SearchForward: "Ctrl+G"' \
			--merged-file "$MERGED" "$LOCAL" "$BASE" "$REMOTE"
	else
		"$merge_tool_path" -X $extra \
			-R 'Accel.SaveAsMerged: "Ctrl+S"' \
			-R 'Accel.Search: "Ctrl+F"' \
			-R 'Accel.SearchForward: "Ctrl+G"' \
			--merged-file "$MERGED" "$LOCAL" "$REMOTE"
	fi
}
